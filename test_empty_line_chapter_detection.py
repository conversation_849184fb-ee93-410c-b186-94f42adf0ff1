#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试空行章节识别功能
验证改进后的章节识别是否能正确识别"上一行为空行"的章节标题
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_content_with_empty_lines():
    """创建包含空行特征的测试内容"""
    return """书名：测试小说
作者：测试作者
===========

这是一些开头的内容，可能包含简介等信息。

第1章 最后三个月

这是第一章的内容。李明走在街上，心情复杂。他知道自己只剩下三个月的时间了。
医生的话还在耳边回响："最多三个月，好好珍惜吧。"
他停下脚步，看着远方的夕阳，心中五味杂陈。

第3章 第一次帮女生穿鞋

王小美坐在台阶上，脚踝扭伤了。李明看到后，主动上前帮忙。
"让我帮你穿鞋吧。"他蹲下身子，小心翼翼地为她穿上鞋子。
这是他第一次为女生做这样的事情，心跳得很快。

第5章 人生开开心心就好

生活总是充满了意外和惊喜。李明渐渐明白，人生最重要的不是长度，而是宽度。
每一天都要开开心心地过，这样才不会留下遗憾。
他开始学会享受当下，珍惜每一个美好的瞬间。

第6章 偷窥幸福

有时候，幸福就在身边，只是我们没有发现。李明学会了观察生活中的小美好。
一朵花的绽放，一只鸟的歌唱，一个陌生人的微笑，都能让他感到温暖。
他开始用不同的眼光看待这个世界。

第10章 朋友之间应该说真话

友谊需要真诚。当朋友问起他的病情时，李明犹豫了很久。
最终，他选择了坦诚相告。朋友们的眼中闪过泪光，但随即给了他更多的关爱。
真话虽然残酷，但它让友谊变得更加珍贵。

第11章 乔迁之喜

李明搬到了一个新的公寓，这里阳光充足，视野开阔。
朋友们都来帮忙搬家，房间里充满了欢声笑语。
这是一个新的开始，也是一个新的希望。

第12章 回天乏术

病情在恶化，医生摇了摇头。李明知道，回天乏术了。
但他没有绝望，反而更加珍惜剩下的时光。
他开始写日记，记录下每一天的感受和思考。

第14章 亲嘴会怀孕

年轻时的无知让人发笑。李明想起了自己小时候的天真想法。
那时候以为亲嘴会怀孕，现在想来真是可爱。
人生就是这样，从无知到成熟，从天真到深刻。

第16章 不回去

家乡的电话一次次响起，但李明选择了不回去。
他不想让家人看到自己憔悴的样子，不想让他们担心。
有些痛苦，只能一个人承受。

第17章 这辈子都别回来

争吵是不可避免的。当朋友劝他回家时，他愤怒地说："这辈子都别回来！"
但说完就后悔了。愤怒只是掩饰内心恐惧的面具。
他需要学会控制自己的情绪，不要伤害关心自己的人。

第18章 我介许你哭出来

眼泪是情感的宣泄。当小美在他面前哭泣时，他轻声说："我介许你哭出来。"
有时候，哭泣比坚强更需要勇气。
他学会了倾听，学会了陪伴，学会了给予安慰。

第20章 坠入爱河

爱情来得突然而美好。尽管时间有限，但李明还是坠入了爱河。
小美的笑容像阳光一样温暖，让他忘记了病痛的折磨。
爱情让生命变得更加有意义。

第21章 不要离开我

"不要离开我。"这是李明内心最深的呼唤。
面对即将到来的分离，他感到前所未有的恐惧。
但他知道，真正的爱是让对方幸福，即使自己要承受痛苦。

第22章 蝴蝶纸音

纸飞机载着思念飞向远方，就像蝴蝶一样轻盈。
李明折了一架纸飞机，写上了自己的心愿。
希望这份美好能够永远保存下去。

第23章 喝酒就喝酒

朋友们聚在一起喝酒，为了忘却烦恼，也为了珍惜友谊。
"喝酒就喝酒，不要想太多。"李明举起酒杯，笑着说道。
这一刻，他们都忘记了忧愁，只有快乐和友谊。

第25章 图书馆恐怖事件

图书馆里发生了一件奇怪的事情。李明在查阅资料时，发现了一本神秘的书。
书中记录着一些不可思议的故事，让他对生命有了新的思考。
有时候，答案就在我们意想不到的地方。

第26章 打你就打你（第1部分）

冲突是不可避免的。当有人欺负小美时，李明愤怒了。
"打你就打你！"他挥起拳头，为了保护心爱的人。
虽然身体虚弱，但内心的力量让他变得勇敢。
"""

class MockGlobalContext:
    """模拟全局上下文"""
    @staticmethod
    def get_logger():
        logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')
        return logging.getLogger(__name__)
    
    @staticmethod
    def get_config():
        class MockConfig:
            @property
            def default_save_dir(self):
                from pathlib import Path
                return Path(".")
        return MockConfig()

def test_empty_line_chapter_detection():
    """测试空行章节识别功能"""
    print("🧪 测试空行章节识别功能")
    print("=" * 50)
    
    # 临时替换
    import novel_src.book_parser.epub_generator as epub_module
    epub_module.GlobalContext = MockGlobalContext
    
    from novel_src.book_parser.epub_generator import EpubGenerator
    
    # 创建测试内容
    test_content = create_test_content_with_empty_lines()
    
    # 创建生成器（启用调试模式）
    parsing_config = {
        'min_chapter_length': 100,  # 降低最小长度以便测试
        'max_chapter_length': 5000,
        'strict_mode': True,
        'auto_merge': False,  # 禁用自动合并以便观察原始识别结果
        'auto_split': False,  # 禁用自动分割
        'debug_mode': True
    }
    
    generator = EpubGenerator(
        identifier="test_empty_line",
        title="空行章节识别测试",
        language="zh-CN",
        author="测试作者",
        description="测试利用空行特征识别章节",
        parsing_config=parsing_config
    )
    
    print("\n📊 分析包含空行特征的文本...")
    analysis = generator.analyze_text_structure(test_content)
    
    if analysis:
        print(f"\n📈 分析结果:")
        print(f"   总行数: {analysis['total_lines']}")
        print(f"   内容开始行: {analysis['content_start_line']}")
        print(f"   处理后行数: {analysis['processed_lines']}")
        print(f"   检测到的章节数: {analysis['detected_chapters']}")
        print(f"   平均章节长度: {analysis['average_chapter_length']:.0f} 字符")
        
        print(f"\n📚 检测到的章节标题:")
        for i, title in enumerate(analysis['chapter_titles'], 1):
            length = analysis['chapter_lengths'][i-1]
            print(f"   {i:2d}. {title} ({length} 字符)")
        
        # 检查是否识别到了所有预期的章节
        expected_chapters = [
            "第1章 最后三个月",
            "第3章 第一次帮女生穿鞋", 
            "第5章 人生开开心心就好",
            "第6章 偷窥幸福",
            "第10章 朋友之间应该说真话",
            "第11章 乔迁之喜",
            "第12章 回天乏术",
            "第14章 亲嘴会怀孕",
            "第16章 不回去",
            "第17章 这辈子都别回来",
            "第18章 我介许你哭出来",
            "第20章 坠入爱河",
            "第21章 不要离开我",
            "第22章 蝴蝶纸音",
            "第23章 喝酒就喝酒",
            "第25章 图书馆恐怖事件",
            "第26章 打你就打你（第1部分）"
        ]
        
        detected_titles = analysis['chapter_titles']
        
        print(f"\n🎯 识别准确性分析:")
        print(f"   期望章节数: {len(expected_chapters)}")
        print(f"   实际识别数: {len(detected_titles)}")
        
        # 检查遗漏的章节
        missing_chapters = []
        for expected in expected_chapters:
            found = False
            for detected in detected_titles:
                if expected.split()[0] in detected:  # 检查章节号是否匹配
                    found = True
                    break
            if not found:
                missing_chapters.append(expected)
        
        if missing_chapters:
            print(f"\n❌ 遗漏的章节 ({len(missing_chapters)} 个):")
            for missing in missing_chapters:
                print(f"     - {missing}")
        else:
            print(f"\n✅ 所有章节都被正确识别！")
        
        # 检查多识别的章节
        extra_chapters = []
        for detected in detected_titles:
            found = False
            for expected in expected_chapters:
                if expected.split()[0] in detected:
                    found = True
                    break
            if not found:
                extra_chapters.append(detected)
        
        if extra_chapters:
            print(f"\n⚠️ 多识别的章节 ({len(extra_chapters)} 个):")
            for extra in extra_chapters:
                print(f"     - {extra}")
    
    else:
        print("❌ 分析失败")
        return False
    
    return True

if __name__ == "__main__":
    test_empty_line_chapter_detection()
