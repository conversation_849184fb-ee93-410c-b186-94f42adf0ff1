#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试具有挑战性的章节识别场景
包含容易误识别的内容，验证空行特征的重要性
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_challenging_test_content():
    """创建包含挑战性内容的测试文本"""
    return """书名：挑战性测试小说
作者：测试作者
===========

这个故事讲述了一个年轻人的成长历程，从懵懂无知到顺理成章地成为一个成熟的人。

第1章 开始的故事

李明是一个普通的大学生。他喜欢读书，经常去图书馆。有一天，他在图书馆里遇到了一个女孩。
这个女孩很漂亮，李明一见钟情。他想要认识她，但是不知道怎么开口。
经过几天的思考，他终于鼓起勇气，走向了那个女孩。

这件事情的发展顺理成章，两个人很快就成为了朋友。李明发现，原来认识一个人并不是那么困难。
他们经常一起学习，一起讨论学术问题。女孩的名字叫小美，是文学系的学生。

第2章 意外的发现

小美告诉李明，她正在写一篇关于古代文学的论文。她说："这篇文章的第三章特别难写。"
李明主动提出帮助她。他们一起查阅资料，讨论古代诗词的韵律和章法。
在研究过程中，李明发现自己对文学也产生了浓厚的兴趣。

有一天，小美说："你知道吗？古代的文章讲究章节分明，每一章都有其独特的作用。"
李明点点头，他开始理解文学作品的结构美。这让他想起了自己正在读的一本小说。

第3章 深入了解

随着时间的推移，李明和小美的关系越来越好。他们不仅是学习伙伴，更成为了知心朋友。
小美经常和他分享自己的想法和感受。她说："人生就像一本书，每个阶段都是一个新的章节。"
这句话让李明深有感触。他开始思考自己的人生轨迹。

在一次讨论中，小美提到了一个有趣的观点："每个人的成长都有其规律，从幼稚到成熟，这是顺理成章的过程。"
李明觉得这个观点很有道理。他开始反思自己的成长经历。

第4章 新的认识

李明开始写日记，记录自己的思考和感悟。他发现，通过文字表达内心的想法是一件很有意思的事情。
他的文章越写越好，文笔也越来越流畅。小美看了他的文章后，给予了很高的评价。
"你的文章很有章法，结构清晰，逻辑严密。"小美这样评价道。

这让李明很高兴。他开始考虑是否要转专业，学习文学。但是这个决定并不容易做出。
他需要考虑很多因素，包括家人的期望、就业前景等等。

第5章 重要的决定

经过深思熟虑，李明决定跟随自己的内心。他向学校申请转专业，从理工科转到文学系。
这个决定让他的家人很惊讶，但是他们最终还是支持了他的选择。
李明知道，这是他人生中一个重要的转折点。

小美得知这个消息后，非常高兴。她说："我相信你会在文学的道路上走得很远。"
李明感谢小美的鼓励和支持。他知道，没有她的帮助，他不可能做出这个决定。

第6章 新的开始

转专业后，李明开始了全新的学习生活。文学系的课程和理工科完全不同，需要更多的思考和感悟。
他努力适应新的学习方式，阅读大量的文学作品，学习写作技巧。
虽然过程很辛苦，但是他感到很充实和快乐。

在一次创作课上，老师布置了一个作业：写一篇短篇小说。李明决定以自己的经历为素材。
他写了一个关于成长和选择的故事，获得了老师和同学们的一致好评。

第7章 收获的季节

时间过得很快，转眼间李明已经在文学系学习了一年。他的写作水平有了很大的提高。
他开始尝试投稿，希望能够发表自己的作品。经过多次尝试，他的一篇散文终于被杂志社采用了。
这是他第一次发表作品，心情非常激动。

小美为他感到高兴，她说："这只是一个开始，我相信你会有更大的成就。"
李明感谢小美一直以来的支持和鼓励。他知道，这份友谊对他来说非常珍贵。

第8章 未来的路

现在，李明对自己的未来充满了信心。他计划继续深造，攻读文学硕士学位。
他希望能够成为一名优秀的作家，用自己的文字感动更多的人。
虽然前路未卜，但是他相信，只要坚持下去，就一定能够实现自己的梦想。

小美也有自己的计划，她准备出国留学，进一步深造。虽然即将分别，但是他们的友谊会永远延续下去。
李明相信，无论走到哪里，他们都会是彼此最好的朋友。

这就是李明的故事，一个关于成长、选择和友谊的故事。每个人的人生都有自己的章节，重要的是要勇敢地书写属于自己的篇章。
"""

class MockGlobalContext:
    """模拟全局上下文"""
    @staticmethod
    def get_logger():
        logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')
        return logging.getLogger(__name__)
    
    @staticmethod
    def get_config():
        class MockConfig:
            @property
            def default_save_dir(self):
                from pathlib import Path
                return Path(".")
        return MockConfig()

def test_challenging_chapter_detection():
    """测试具有挑战性的章节识别"""
    print("🧪 测试具有挑战性的章节识别")
    print("=" * 50)
    
    # 临时替换
    import novel_src.book_parser.epub_generator as epub_module
    epub_module.GlobalContext = MockGlobalContext
    
    from novel_src.book_parser.epub_generator import EpubGenerator
    
    # 创建测试内容
    test_content = create_challenging_test_content()
    
    # 创建生成器（启用调试模式）
    parsing_config = {
        'min_chapter_length': 100,  # 降低最小长度以便测试
        'max_chapter_length': 5000,
        'strict_mode': True,
        'auto_merge': False,  # 禁用自动合并以便观察原始识别结果
        'auto_split': False,  # 禁用自动分割
        'debug_mode': True
    }
    
    generator = EpubGenerator(
        identifier="test_challenging",
        title="挑战性章节识别测试",
        language="zh-CN",
        author="测试作者",
        description="测试包含干扰内容的章节识别",
        parsing_config=parsing_config
    )
    
    print("\n📊 分析包含挑战性内容的文本...")
    print("注意：文本中包含'顺理成章'、'章法'、'章节'等可能干扰识别的词汇")
    
    analysis = generator.analyze_text_structure(test_content)
    
    if analysis:
        print(f"\n📈 分析结果:")
        print(f"   总行数: {analysis['total_lines']}")
        print(f"   检测到的章节数: {analysis['detected_chapters']}")
        
        print(f"\n📚 检测到的章节标题:")
        for i, title in enumerate(analysis['chapter_titles'], 1):
            length = analysis['chapter_lengths'][i-1]
            print(f"   {i:2d}. {title} ({length} 字符)")
        
        # 检查是否正确识别了真正的章节，而没有被干扰内容误导
        expected_chapters = [
            "第1章 开始的故事",
            "第2章 意外的发现", 
            "第3章 深入了解",
            "第4章 新的认识",
            "第5章 重要的决定",
            "第6章 新的开始",
            "第7章 收获的季节",
            "第8章 未来的路"
        ]
        
        detected_titles = analysis['chapter_titles']
        
        print(f"\n🎯 识别准确性分析:")
        print(f"   期望章节数: {len(expected_chapters)}")
        print(f"   实际识别数: {len(detected_titles)}")
        
        # 检查是否正确识别
        correct_count = 0
        for expected in expected_chapters:
            for detected in detected_titles:
                if expected.split()[0] in detected:  # 检查章节号是否匹配
                    correct_count += 1
                    break
        
        print(f"   正确识别数: {correct_count}")
        print(f"   识别准确率: {correct_count/len(expected_chapters)*100:.1f}%")
        
        # 检查是否有误识别（被干扰内容误导）
        false_positives = []
        for detected in detected_titles:
            is_valid = False
            for expected in expected_chapters:
                if expected.split()[0] in detected:
                    is_valid = True
                    break
            if not is_valid:
                false_positives.append(detected)
        
        if false_positives:
            print(f"\n❌ 误识别的内容 ({len(false_positives)} 个):")
            for fp in false_positives:
                print(f"     - {fp}")
        else:
            print(f"\n✅ 没有误识别！成功避免了干扰内容的影响")
    
    else:
        print("❌ 分析失败")
        return False
    
    return True

if __name__ == "__main__":
    test_challenging_chapter_detection()
