#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试番外类型章节标题的识别
验证改进后的章节识别是否能正确识别各种番外格式
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_fanwai_test_content():
    """创建包含各种番外格式的测试内容"""
    return """书名：测试小说
作者：测试作者
===========

这是正文的开始部分，包含一些基本的故事内容。

第1章 主线故事开始

这是第一章的内容。主角开始了他的冒险之旅。
故事情节紧张刺激，读者们都很期待后续的发展。

第2章 情节发展

故事继续发展，主角遇到了各种挑战和困难。
他必须克服这些障碍，才能继续前进。

番外 《梦蝶庄生》实体书8月10日预售通知

亲爱的读者们，我们很高兴地宣布，《梦蝶庄生》实体书将于8月10日开始预售！
这本书包含了完整的故事内容，以及一些独家的番外篇章。
预售期间将有特别的优惠活动，敬请关注我们的官方公告。

第3章 继续主线

主线故事继续进行，主角的能力得到了进一步的提升。
他开始理解自己的使命和责任。

番外篇 角色背景设定

这里详细介绍了主要角色的背景设定和人物关系。
包括他们的出身、性格特点、能力设定等重要信息。
这些信息有助于读者更好地理解故事情节。

第4章 高潮部分

故事进入高潮部分，各种矛盾和冲突集中爆发。
主角面临着前所未有的挑战。

外传 平行世界的故事

在另一个平行世界中，同样的角色有着不同的命运。
这个外传探索了"如果"的可能性，展现了不同的故事走向。

第5章 结局

经过重重困难，主角终于完成了自己的使命。
故事迎来了圆满的结局。

特别篇 作者后记

感谢所有读者的支持和陪伴。
这个故事的创作过程充满了挑战，但也带来了很多快乐。
希望这个故事能够给大家带来一些思考和感动。

附录 世界观设定详解

这里详细说明了故事世界的各种设定。
包括地理环境、历史背景、魔法体系等详细信息。

番外 读者见面会活动通知

我们将在下个月举办读者见面会活动。
届时作者将与读者面对面交流，分享创作心得。
活动详情请关注我们的官方网站。

特典 限定版内容说明

限定版将包含一些特殊的内容和周边产品。
包括角色设定集、作者签名、限定插画等。
数量有限，先到先得。
"""

class MockGlobalContext:
    """模拟全局上下文"""
    @staticmethod
    def get_logger():
        logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')
        return logging.getLogger(__name__)
    
    @staticmethod
    def get_config():
        class MockConfig:
            @property
            def default_save_dir(self):
                from pathlib import Path
                return Path(".")
        return MockConfig()

def test_fanwai_chapter_detection():
    """测试番外类型章节识别功能"""
    print("🧪 测试番外类型章节识别功能")
    print("=" * 50)
    
    # 临时替换
    import novel_src.book_parser.epub_generator as epub_module
    epub_module.GlobalContext = MockGlobalContext
    
    from novel_src.book_parser.epub_generator import EpubGenerator
    
    # 创建测试内容
    test_content = create_fanwai_test_content()
    
    # 创建生成器（启用调试模式）
    parsing_config = {
        'min_chapter_length': 50,  # 降低最小长度以便测试番外内容
        'max_chapter_length': 5000,
        'strict_mode': True,
        'auto_merge': False,  # 禁用自动合并以便观察原始识别结果
        'auto_split': False,  # 禁用自动分割
        'debug_mode': True
    }
    
    generator = EpubGenerator(
        identifier="test_fanwai",
        title="番外章节识别测试",
        language="zh-CN",
        author="测试作者",
        description="测试各种番外格式的章节识别",
        parsing_config=parsing_config
    )
    
    print("\n📊 分析包含番外内容的文本...")
    analysis = generator.analyze_text_structure(test_content)
    
    if analysis:
        print(f"\n📈 分析结果:")
        print(f"   总行数: {analysis['total_lines']}")
        print(f"   检测到的章节数: {analysis['detected_chapters']}")
        
        print(f"\n📚 检测到的章节标题:")
        for i, title in enumerate(analysis['chapter_titles'], 1):
            length = analysis['chapter_lengths'][i-1]
            chapter_type = "📖 正章" if title.startswith("第") else "🌟 特殊"
            print(f"   {i:2d}. {chapter_type} {title} ({length} 字符)")
        
        # 检查是否识别到了所有预期的章节
        expected_chapters = [
            "第1章 主线故事开始",
            "第2章 情节发展", 
            "番外 《梦蝶庄生》实体书8月10日预售通知",
            "第3章 继续主线",
            "番外篇 角色背景设定",
            "第4章 高潮部分",
            "外传 平行世界的故事",
            "第5章 结局",
            "特别篇 作者后记",
            "附录 世界观设定详解",
            "番外 读者见面会活动通知",
            "特典 限定版内容说明"
        ]
        
        detected_titles = analysis['chapter_titles']
        
        print(f"\n🎯 识别准确性分析:")
        print(f"   期望章节数: {len(expected_chapters)}")
        print(f"   实际识别数: {len(detected_titles)}")
        
        # 检查遗漏的章节
        missing_chapters = []
        for expected in expected_chapters:
            found = False
            for detected in detected_titles:
                # 对于番外类型，检查关键词匹配
                if expected.startswith("第"):
                    if expected.split()[0] in detected:
                        found = True
                        break
                else:
                    # 对于番外、外传等，检查关键词
                    expected_key = expected.split()[0]
                    if expected_key in detected:
                        found = True
                        break
            if not found:
                missing_chapters.append(expected)
        
        if missing_chapters:
            print(f"\n❌ 遗漏的章节 ({len(missing_chapters)} 个):")
            for missing in missing_chapters:
                print(f"     - {missing}")
        else:
            print(f"\n✅ 所有章节都被正确识别！")
        
        # 特别检查关键的番外标题
        key_fanwai_found = False
        for detected in detected_titles:
            if "梦蝶庄生" in detected and "预售通知" in detected:
                key_fanwai_found = True
                print(f"\n🎯 关键番外标题识别成功: {detected}")
                break
        
        if not key_fanwai_found:
            print(f"\n❌ 关键番外标题未识别: 番外 《梦蝶庄生》实体书8月10日预售通知")
    
    else:
        print("❌ 分析失败")
        return False
    
    return True

if __name__ == "__main__":
    test_fanwai_chapter_detection()
