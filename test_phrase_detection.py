#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试成语和短语误识别问题
专门测试"顺理成章"等包含"章"字的词语不会被误识别为章节标题
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_phrase_detection():
    """测试短语检测功能"""
    print("🔍 测试成语和短语误识别问题...")
    
    # 创建包含容易误识别内容的测试文本
    test_content = """书名：测试小说
作者：测试作者
简介：这是一个测试小说
====================

第1章 开始

这是第一章的内容。主人公开始了他的冒险之旅。

第2章 发展

班长辞职，由副班长接任，顺理成章。这是很自然的事情。

主人公继续他的故事，遇到了各种挑战和机遇。

第3章 转折

故事发生了意想不到的转折。文章写得很精彩，让人印象深刻。

规章制度需要遵守，这是基本的要求。

第4章 高潮

这一章是全书的高潮部分。乐章响起，气氛达到了顶点。

篇章结构安排得很巧妙，作者的章法运用得当。

第5章 结局

故事迎来了结局。一切都按照章程进行，没有违章行为。

尾声

这就是整个故事的结束。成章立说，别开生面的结局。
"""
    
    # 设置mock环境
    import logging
    logging.basicConfig(level=logging.INFO, format='%(message)s')
    logger = logging.getLogger('test')
    
    class MockGlobalContext:
        @staticmethod
        def get_logger():
            return logger
        
        @staticmethod
        def get_config():
            class MockConfig:
                default_save_dir = Path.cwd() / 'temp'
            return MockConfig()
    
    # 临时替换
    import novel_src.book_parser.epub_generator as epub_module
    epub_module.GlobalContext = MockGlobalContext
    
    from novel_src.book_parser.epub_generator import EpubGenerator
    
    # 创建生成器
    parsing_config = {
        'min_chapter_length': 100,  # 降低阈值用于测试
        'max_chapter_length': 5000,
        'strict_mode': True,        # 启用严格模式
        'auto_merge': False,        # 禁用自动合并，便于观察原始识别结果
        'auto_split': False,        # 禁用自动分割
        'debug_mode': True
    }
    
    generator = EpubGenerator(
        identifier="phrase_test",
        title="短语测试",
        language="zh-CN",
        author="测试作者",
        description="测试成语误识别",
        parsing_config=parsing_config
    )
    
    print("\n📊 分析包含成语的文本...")
    analysis = generator.analyze_text_structure(test_content)
    
    if analysis:
        print(f"\n✅ 分析结果:")
        print(f"   检测到章节数: {analysis['detected_chapters']}")
        
        print(f"\n📚 检测到的章节标题:")
        for i, title in enumerate(analysis['chapter_titles'], 1):
            # 检查是否包含不应该被识别的短语
            problematic_phrases = ['顺理成章', '文章', '规章', '乐章', '篇章', '章法', '章程', '违章', '成章']
            is_problematic = any(phrase in title for phrase in problematic_phrases)
            status = "❌ 误识别" if is_problematic else "✅ 正确"
            print(f"   {i:2d}. {status} {title}")
        
        # 统计误识别情况
        problematic_count = 0
        for title in analysis['chapter_titles']:
            if any(phrase in title for phrase in ['顺理成章', '文章', '规章', '乐章', '篇章', '章法', '章程', '违章', '成章']):
                problematic_count += 1
        
        print(f"\n📈 测试结果:")
        print(f"   总章节数: {analysis['detected_chapters']}")
        print(f"   误识别数: {problematic_count}")
        print(f"   准确率: {((analysis['detected_chapters'] - problematic_count) / analysis['detected_chapters'] * 100):.1f}%" if analysis['detected_chapters'] > 0 else "N/A")
        
        if problematic_count == 0:
            print("🎉 测试通过！没有发现成语误识别问题。")
        else:
            print(f"⚠️ 发现 {problematic_count} 个误识别问题，需要进一步优化。")
    
    else:
        print("❌ 分析失败")


if __name__ == "__main__":
    test_phrase_detection()
